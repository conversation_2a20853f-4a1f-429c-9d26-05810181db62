# -*- coding: utf-8 -*-
"""
GUI股票分析模块
包含股票分析的核心逻辑
"""

import os
import threading
import traceback
from tkinter import messagebox

from compass_automator import CompassAutomator


class GUIAnalysisMixin:
    """GUI股票分析Mixin类"""
    
    def start_analysis(self):
        """开始分析或继续分析"""
        # 如果是暂停状态，直接继续分析
        if self.is_paused:
            self.message_queue.put(("log", "继续分析..."))
        else:
            # 全新分析需要检查Excel文件
            if not self.current_excel_path:
                messagebox.showerror("错误", "请先选择Excel文件")
                return
                
            if not os.path.exists(self.current_excel_path):
                messagebox.showerror("错误", "Excel文件不存在")
                return
            
            # 清空结果表格（只在全新分析时清空）
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)
            
            # 清空失败股票列表（只在全新分析时清空）
            self.failed_stocks = []
            
            # 添加快捷键提示
            self.message_queue.put(("log", "开始分析... (按Ctrl+Alt+S可暂停)"))
            
        self.is_processing = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.save_btn.config(state="disabled")
            
        # 启动分析线程
        analysis_thread = threading.Thread(target=self.analysis_worker, daemon=True)
        analysis_thread.start()
        
    def analysis_worker(self):
        """分析工作线程"""
        try:
            # 检查是否为继续分析
            if self.is_paused and self.stock_codes_cache:
                # 继续分析：使用缓存的股票代码
                stock_codes = self.stock_codes_cache
                start_index = self.current_stock_index
                self.message_queue.put(("log", f"继续分析：从第 {start_index + 1} 个股票开始"))
                self.is_paused = False
            else:
                # 全新分析：读取Excel文件
                self.message_queue.put(("status", "正在读取Excel文件..."))
                self.message_queue.put(("log", f"开始读取Excel文件: {self.current_excel_path}"))
                
                stock_codes = self.data_processor.load_excel_file(self.current_excel_path)
                start_index = 0
                self.current_stock_index = 0
                
                # 缓存股票代码列表以备暂停后继续使用
                self.stock_codes_cache = stock_codes
                
                # 初始化实时保存功能（仅在全新分析时）
                self.message_queue.put(("status", "正在初始化实时保存功能..."))
                if self.data_processor.init_realtime_save():
                    self.message_queue.put(("log", f"实时保存已初始化，符合条件的股票将自动保存到analysis_results目录"))
                else:
                    self.message_queue.put(("log", "⚠ 实时保存初始化失败，将跳过自动保存功能"))
            
            total_stocks = len(stock_codes)
            self.message_queue.put(("log", f"共需分析 {total_stocks} 个股票代码，当前从第 {start_index + 1} 个开始"))
            
            # 重置暂停请求标志
            self.pause_requested = False
            
            # 检查指南针自动化连接状态
            self.message_queue.put(("status", "正在检查指南针软件连接..."))
            
            # 特殊处理：如果是继续分析，连接应该仍然有效
            if self.is_paused and self.compass_automator:
                # 继续分析时，首先验证现有连接
                if self.compass_automator.check_connection_valid():
                    self.logger.info("继续分析：使用保持的指南针连接")
                    
                    # 确保鼠标重置回调函数被正确设置
                    if not self.compass_automator.mouse_reset_callback:
                        self.logger.info("设置缺失的鼠标重置回调函数")
                        self.compass_automator.mouse_reset_callback = self.move_mouse_to_target_position
                    
                    compass_ready = True
                else:
                    # 如果连接无效，记录异常并重新连接
                    self.logger.warning("继续分析时发现连接已断开，这可能是一个问题")
                    compass_ready = self.compass_automator.start_compass_software()
            else:
                # 全新分析或其他情况的连接检查
                if self.compass_automator and self.compass_automator.check_connection_valid():
                    self.logger.info("使用已存在的指南针连接")
                    
                    # 确保鼠标重置回调函数被正确设置
                    if not self.compass_automator.mouse_reset_callback:
                        self.logger.info("设置缺失的鼠标重置回调函数")
                        self.compass_automator.mouse_reset_callback = self.move_mouse_to_target_position
                    
                    compass_ready = True
                else:
                    # 如果没有实例或连接失效，尝试重新连接
                    if not self.compass_automator:
                        self.logger.warning("指南针实例不存在，创建新实例")
                        from compass_automator import CompassAutomator
                        self.compass_automator = CompassAutomator(ocr_manager=self.ocr_manager, mouse_reset_callback=self.move_mouse_to_target_position)
                    else:
                        self.logger.info("指南针连接已断开，尝试重新连接")
                    
                    # 尝试连接到指南针软件
                    compass_ready = self.compass_automator.start_compass_software()
            
            if not compass_ready:
                self.message_queue.put(("log", "无法连接到指南针软件"))
                self.message_queue.put(("status", "连接指南针软件失败"))
                return
                
            # 分析每只股票（从指定索引开始）
            all_results = []  # 存储所有分析结果
            for i in range(start_index, total_stocks):
                if not self.is_processing:
                    break
                    
                # 更新当前处理索引
                self.current_stock_index = i
                stock_code = stock_codes[i]
                
                progress = (i + 1) / total_stocks * 100
                self.message_queue.put(("progress", progress))
                self.message_queue.put(("status", f"正在分析 {stock_code} ({i+1}/{total_stocks})"))
                
                # 添加醒目的开始分析标识
                self.message_queue.put(("log", f"=== 开始分析股票: {stock_code} ({i+1}/{total_stocks}) ==="))
                
                # 分析股票
                result = self.compass_automator.analyze_single_stock(stock_code)
                all_results.append(result)
                
                # 显示分析结果
                status = result.get('status', '未知状态')
                
                if status == '分析成功':
                    # 获取三天资金数据
                    today_fund = result.get('today_fund', 0.0)
                    yesterday_fund = result.get('yesterday_fund', 0.0)
                    day_before_fund = result.get('day_before_yesterday_fund', 0.0)
                    
                    # 清晰显示成功结果和三天数据
                    self.message_queue.put(("log", f"股票 {stock_code} 分析成功"))
                    self.message_queue.put(("log", f"  今日: {today_fund:+.3f}% | 昨日: {yesterday_fund:+.3f}% | 前日: {day_before_fund:+.3f}%"))
                    
                    # 实时保存符合条件的股票
                    try:
                        saved = self.data_processor.save_qualified_stock_realtime(result)
                        if saved:
                            self.message_queue.put(("log", f"  ✓ 股票 {stock_code} 符合条件，已实时保存到analysis_results目录"))
                        else:
                            # 如果保存失败但不是因为不符合条件，记录错误
                            if self.data_processor._meets_filter_condition(today_fund, yesterday_fund, day_before_fund):
                                self.message_queue.put(("log", f"  ⚠ 股票 {stock_code} 符合条件但保存失败"))
                    except Exception as e:
                        self.logger.error(f"实时保存股票 {stock_code} 时发生异常: {str(e)}")
                        self.message_queue.put(("log", f"  ⚠ 股票 {stock_code} 实时保存异常"))
                else:
                    # 显示失败结果
                    self.message_queue.put(("log", f"股票 {stock_code} 分析失败：{status}"))
                    
                    # 记录失败股票
                    self.failed_stocks.append({
                        'stock_code': stock_code,
                        'reason': status
                    })
                
                # 检查是否请求暂停（在每个股票分析完成后）
                if self.pause_requested:
                    self.message_queue.put(("log", f"暂停分析：已完成 {i+1} 个股票，剩余 {total_stocks - i - 1} 个"))
                    self.message_queue.put(("status", "分析已暂停"))
                    
                    # 保存当前进度
                    self.current_stock_index = i + 1  # 下次从下一个股票开始
                    self.is_paused = True
                    
                    # 暂停时保持连接，不断开指南针软件
                    self.should_keep_connection = True
                    
                    # 发送暂停完成消息
                    self.message_queue.put(("analysis_paused", None))
                    return
                
            # 如果执行到这里说明没有暂停，完成了所有分析
            # 存储所有结果以供保存使用
            self.all_analysis_results = all_results
            
            # 筛选结果
            self.message_queue.put(("status", "正在筛选结果..."))
            filtered_results = self.data_processor.filter_stocks(all_results)
            
            # 更新表格显示筛选结果
            self.message_queue.put(("filtered_results", filtered_results))
            
            # 显示统计信息
            stats = self.data_processor.get_statistics()
            success_count = len([r for r in all_results if r.get('status') == '分析成功'])
            failed_count = len(all_results) - success_count
            
            self.message_queue.put(("log", f"分析统计: 总计 {len(all_results)} 只股票"))
            self.message_queue.put(("log", f"  成功分析: {success_count} 只"))
            self.message_queue.put(("log", f"  分析失败: {failed_count} 只"))
            self.message_queue.put(("log", f"  符合筛选条件: {stats['filtered_stocks']} 只"))
            
            # 显示实时保存统计信息
            if self.data_processor.realtime_save_file:
                if os.path.exists(self.data_processor.realtime_save_file):
                    self.message_queue.put(("log", f"  实时保存: 符合条件的股票已保存到analysis_results目录"))
                    self.message_queue.put(("log", f"  保存文件: {os.path.basename(self.data_processor.realtime_save_file)}"))
                else:
                    self.message_queue.put(("log", f"  实时保存: 无符合条件的股票需要保存"))
            
            self.message_queue.put(("status", "分析完成"))
            self.message_queue.put(("enable_save", True))
            
            # 重置暂停状态（分析全部完成）
            self.is_paused = False
            self.current_stock_index = 0
            self.stock_codes_cache = []
            
            # 分析完成，允许断开连接
            self.should_keep_connection = False
            
        except Exception as e:
            self.message_queue.put(("log", f"分析过程中发生错误: {str(e)}"))
            self.message_queue.put(("log", traceback.format_exc()))
            self.message_queue.put(("status", "分析失败"))
            
            # 发生错误，允许断开连接
            self.should_keep_connection = False
        finally:
            # 导出失败股票记录（如果有失败的股票）
            if hasattr(self, 'failed_stocks') and self.failed_stocks:
                try:
                    output_path = self.data_processor.export_failed_stocks(self.failed_stocks)
                    if output_path:
                        self.message_queue.put(("log", f"⚠ 失败股票已导出: {os.path.basename(output_path)}"))
                        self.message_queue.put(("log", f"  共记录 {len(self.failed_stocks)} 只失败股票"))
                    else:
                        self.message_queue.put(("log", "⚠ 失败股票导出失败"))
                except Exception as e:
                    self.logger.error(f"导出失败股票记录时发生异常: {str(e)}")
                    self.message_queue.put(("log", f"⚠ 导出失败股票记录异常: {str(e)}"))
            
            self.is_processing = False
            self.message_queue.put(("analysis_complete", None))
            
            # 根据should_keep_connection标志决定是否断开连接
            if self.compass_automator:
                if self.should_keep_connection:
                    # 暂停状态，保持连接
                    self.logger.info("分析暂停，保持指南针软件连接")
                else:
                    # 正常结束或停止，断开连接
                    self.logger.info("分析结束，断开指南针软件连接")
                    self.compass_automator.close_compass_software()
                
    def stop_analysis(self):
        """停止分析"""
        # 检查是否从暂停状态停止
        was_paused = self.is_paused
        
        self.is_processing = False
        # 重置暂停相关状态
        self.is_paused = False
        self.pause_requested = False
        self.current_stock_index = 0
        self.stock_codes_cache = []
        
        # 重置失败股票列表
        if hasattr(self, 'failed_stocks'):
            self.failed_stocks = []
        
        # 重置实时保存状态
        if hasattr(self, 'data_processor'):
            self.data_processor.analysis_start_time = None
            self.data_processor.realtime_save_file = None
        
        # 停止分析时允许断开连接
        self.should_keep_connection = False
        
        if was_paused:
            self.message_queue.put(("log", "用户从暂停状态停止分析"))
            # 从暂停状态停止时，需要立即断开连接，因为analysis_worker线程已结束
            if self.compass_automator:
                self.logger.info("从暂停状态停止，立即断开指南针软件连接")
                self.compass_automator.close_compass_software()
            # 发送特殊消息确保按钮状态正确恢复
            self.message_queue.put(("analysis_stopped_from_pause", None))
        else:
            self.message_queue.put(("log", "用户请求停止分析"))
            
        self.message_queue.put(("status", "正在停止分析..."))