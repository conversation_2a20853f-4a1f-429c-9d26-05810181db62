# -*- coding: utf-8 -*-
"""
简化的PaddleOCR引擎实现
基于paddleocr_debug.py的成功经验，直接使用PaddleOCR处理原始图片
避免复杂的预处理策略，提高识别准确率
"""

import numpy as np
import cv2
import logging
import time
import traceback
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import re


class SimplePaddleOCREngine:
    """简化的PaddleOCR引擎"""
    
    def __init__(self, use_gpu: bool = False, debug_mode: bool = False):
        """
        初始化简化的PaddleOCR引擎
        
        Args:
            use_gpu: 保留参数兼容性，但实际强制使用CPU
            debug_mode: 是否启用调试模式
        """
        self.logger = logging.getLogger(__name__)
        self.debug_mode = debug_mode
        self.use_gpu = False  # 强制设为False，不论传入什么值
        self.paddle_reader = None
        self.is_initialized = False
        
        # 导入配置
        from config import APP_CONFIG
        self.config = APP_CONFIG.get('ocr_settings', {})
        self.local_models_config = self.config.get('local_models', {})
        
        # 初始化PaddleOCR
        self._init_paddleocr()
    
    def _init_paddleocr(self):
        """初始化PaddleOCR"""
        try:
            self.logger.info("正在初始化PaddleOCR引擎 (CPU模式)...")
            
            import paddleocr
            import os
            
            # 准备初始化参数 - 移除use_gpu参数，仅使用兼容参数
            init_params = {
                'lang': 'ch'
            }
            
            # 检查是否启用本地模型
            if self.local_models_config.get('enabled', False):
                models_dir = self.local_models_config.get('models_dir', 'models')
                models = self.local_models_config.get('models', {})
                
                # 构建检测模型路径
                if models.get('detection', {}).get('enabled', False):
                    det_model_name = models['detection']['model_name']
                    det_model_path = os.path.join(models_dir, det_model_name)
                    if os.path.exists(det_model_path):
                        init_params['det_model_dir'] = det_model_path
                        init_params['text_detection_model_name'] = det_model_name
                        self.logger.info(f"使用本地检测模型: {det_model_path} (名称: {det_model_name})")
                    else:
                        self.logger.warning(f"本地检测模型路径不存在: {det_model_path}")
                
                # 构建识别模型路径
                if models.get('recognition', {}).get('enabled', False):
                    rec_model_name = models['recognition']['model_name']
                    rec_model_path = os.path.join(models_dir, rec_model_name)
                    if os.path.exists(rec_model_path):
                        init_params['rec_model_dir'] = rec_model_path
                        init_params['text_recognition_model_name'] = rec_model_name
                        self.logger.info(f"使用本地识别模型: {rec_model_path} (名称: {rec_model_name})")
                    else:
                        self.logger.warning(f"本地识别模型路径不存在: {rec_model_path}")
            
            # 初始化PaddleOCR
            self.paddle_reader = paddleocr.PaddleOCR(**init_params)
            
            self.is_initialized = True
            self.logger.info("PaddleOCR引擎初始化成功 (CPU模式)")
            
        except Exception as e:
            self.logger.error(f"PaddleOCR引擎初始化失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"详细错误: {traceback.format_exc()}")
            
            # 如果本地模型初始化失败，尝试使用默认模式
            if self.local_models_config.get('enabled', False):
                try:
                    self.logger.info("本地模型初始化失败，尝试默认模式...")
                    import paddleocr
                    
                    # 默认模式也移除use_gpu参数
                    self.paddle_reader = paddleocr.PaddleOCR(lang='ch')
                    
                    self.is_initialized = True
                    self.use_gpu = False
                    self.logger.info("PaddleOCR引擎初始化成功 (默认模式回退)")
                    
                except Exception as e2:
                    self.logger.error(f"默认模式初始化也失败: {str(e2)}")
                    self.is_initialized = False
    
    def is_available(self) -> bool:
        """检查PaddleOCR是否可用"""
        return self.is_initialized and self.paddle_reader is not None
    
    def recognize_image(self, image: np.ndarray, retry_count: int = 0) -> List[str]:
        """
        识别图像中的文本（直接处理原始图片）
        
        Args:
            image: 输入图像
            retry_count: 当前重试次数
            
        Returns:
            识别到的文本列表
        """
        max_retries = 3  # 最大重试次数
        
        if not self.is_available():
            self.logger.error("PaddleOCR引擎未初始化")
            return []
        
        try:
            if self.debug_mode:
                self.logger.debug(f"开始PaddleOCR识别，图像尺寸: {image.shape}，重试次数: {retry_count}")
            
            # 直接使用PaddleOCR识别原始图像，不进行预处理
            # 使用新的predict方法替代已弃用的ocr方法
            if hasattr(self.paddle_reader, 'predict'):
                results = self.paddle_reader.predict(image)
            else:
                # 回退到旧方法以保持兼容性
                results = self.paddle_reader.ocr(image)
            
            if self.debug_mode:
                self.logger.debug(f"PaddleOCR原始结果: {results}")
            
            # 提取文本，参考paddleocr_debug.py的实现
            texts = self._extract_texts_from_results(results)
            
            if self.debug_mode:
                self.logger.debug(f"提取到的文本: {texts}")
            
            return texts
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"PaddleOCR识别失败: {error_msg}")
            
            # 检查是否是Unknown exception，且还有重试机会
            if "Unknown exception" in error_msg and retry_count < max_retries:
                self.logger.warning(f"检测到PaddleOCR Unknown exception，尝试重新初始化引擎 (重试 {retry_count + 1}/{max_retries})")
                
                # 重新初始化PaddleOCR引擎
                if self._reinitialize_paddleocr():
                    self.logger.info("PaddleOCR引擎重新初始化成功，重试识别")
                    # 递归重试
                    return self.recognize_image(image, retry_count + 1)
                else:
                    self.logger.error("PaddleOCR引擎重新初始化失败")
            
            if self.debug_mode:
                self.logger.debug(f"详细错误: {traceback.format_exc()}")
            return []
    
    def _reinitialize_paddleocr(self) -> bool:
        """
        重新初始化PaddleOCR引擎
        
        Returns:
            重新初始化是否成功
        """
        try:
            self.logger.info("开始重新初始化PaddleOCR引擎...")
            
            # 清理现有实例
            self.paddle_reader = None
            self.is_initialized = False
            
            # 短暂等待，让系统释放资源
            time.sleep(0.5)
            
            # 重新初始化（会自动使用本地模型配置）
            self._init_paddleocr()
            
            if self.is_available():
                self.logger.info("PaddleOCR引擎重新初始化成功")
                return True
            else:
                self.logger.error("PaddleOCR引擎重新初始化失败")
                return False
                
        except Exception as e:
            self.logger.error(f"重新初始化PaddleOCR引擎时发生异常: {str(e)}")
            return False
    
    def _extract_texts_from_results(self, results) -> List[str]:
        """
        从PaddleOCR结果中提取文本
        参考paddleocr_debug.py的实现
        """
        texts = []
        
        if not results:
            return texts
        
        try:
            # 处理新的OCRResult格式
            for result in results:
                # 检查是否是OCRResult对象
                if hasattr(result, '__dict__') or isinstance(result, dict):
                    # 尝试从字典或对象中提取rec_texts
                    rec_texts = None
                    
                    if isinstance(result, dict):
                        rec_texts = result.get('rec_texts', [])
                    else:
                        # 对象属性访问
                        rec_texts = getattr(result, 'rec_texts', [])
                    
                    # 添加识别到的文本
                    if rec_texts:
                        for text in rec_texts:
                            if text and text.strip():
                                texts.append(text.strip())
                else:
                    # 处理传统格式：[[bbox, (text, confidence)], ...]
                    if isinstance(result, list):
                        for item in result:
                            if isinstance(item, list) and len(item) >= 2:
                                # 提取文本部分
                                text_info = item[1]
                                if isinstance(text_info, tuple) and len(text_info) >= 1:
                                    text = text_info[0]
                                    if text and text.strip():
                                        texts.append(text.strip())
                                elif isinstance(text_info, str):
                                    if text_info.strip():
                                        texts.append(text_info.strip())
                                        
        except Exception as e:
            self.logger.error(f"文本提取失败: {e}")
            if self.debug_mode:
                self.logger.debug(f"详细错误: {traceback.format_exc()}")
        
        return texts
    
    def recognize_fund_data(self, image: np.ndarray) -> List[float]:
        """
        识别资金数据（专门用于股票资金识别）
        
        Args:
            image: 输入图像
            
        Returns:
            识别到的资金数值列表
        """
        # 获取当前的OCR图像缩放配置
        from config import APP_CONFIG
        scale_mode = APP_CONFIG.get('ocr_settings', {}).get('image_scale_mode', 'original')
        
        # 根据配置对图像进行预处理
        processed_image = image
        if scale_mode != 'original':
            processed_image = self._apply_image_scaling(image, scale_mode)
            if self.debug_mode:
                self.logger.debug(f"应用OCR图像缩放模式: {scale_mode}")
        
        texts = self.recognize_image(processed_image)
        fund_values = []
        
        # 解析资金数据的正则表达式
        fund_pattern = r'[-+]?\d+\.?\d*%?'
        
        for text in texts:
            # 查找所有可能的数值
            matches = re.findall(fund_pattern, text)
            for match in matches:
                try:
                    # 移除百分号并转换为浮点数
                    value_str = match.replace('%', '')
                    value = float(value_str)
                    fund_values.append(value)
                    
                    if self.debug_mode:
                        self.logger.debug(f"解析资金数据: '{match}' -> {value}")
                        
                except ValueError:
                    continue
        
        return fund_values
    
    def _apply_image_scaling(self, image: np.ndarray, scale_mode: str) -> np.ndarray:
        """
        根据缩放模式对图像进行缩放处理
        
        Args:
            image: 输入图像
            scale_mode: 缩放模式 ('2x', '4x')
            
        Returns:
            缩放后的图像
        """
        try:
            # 确定缩放因子
            scale_factor_map = {
                '2x': 2,
                '4x': 4
            }
            
            scale_factor = scale_factor_map.get(scale_mode, 1)
            if scale_factor == 1:
                return image
            
            # 获取原始图像尺寸
            height, width = image.shape[:2]
            new_width = width * scale_factor
            new_height = height * scale_factor
            
            # 使用高质量插值方法放大图像（参考FundOCRValidator实现）
            scaled_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            if self.debug_mode:
                self.logger.debug(f"图像缩放: {width}x{height} -> {new_width}x{new_height} (倍率: {scale_factor})")
            
            return scaled_image
            
        except Exception as e:
            self.logger.error(f"图像缩放处理失败: {str(e)}")
            return image  # 返回原图像作为回退
    
    def capture_and_recognize_region(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        截取屏幕区域并进行OCR识别
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域宽高
            
        Returns:
            识别结果字典
        """
        try:
            # 截取屏幕区域
            screenshot = self._capture_screen_region(x, y, width, height)
            if screenshot is None:
                return {'success': False, 'error': '截图失败'}
            
            # 识别资金数据
            fund_values = self.recognize_fund_data(screenshot)
            
            return {
                'success': True,
                'fund_values': fund_values,
                'strategy': 'simple_paddleocr',
                'confidence': 1.0 if fund_values else 0.0,
                'total_strategies': 1,
                'strategies_tried': 1,
                'early_exit': True,
                'execution_time': 0.0
            }
            
        except Exception as e:
            self.logger.error(f"区域识别失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _capture_screen_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """
        截取屏幕区域

        Args:
            x, y: 区域左上角坐标
            width, height: 区域宽高

        Returns:
            截图图像或None
        """
        try:
            # 使用现有的图像处理器进行截图
            from image_processor import ImageProcessor

            image_processor = ImageProcessor(debug_mode=self.debug_mode)
            screenshot = image_processor.capture_region(x, y, width, height)

            if self.debug_mode and screenshot is not None:
                self.logger.debug(f"截取屏幕区域: ({x}, {y}, {width}, {height}), 图像尺寸: {screenshot.shape}")

            return screenshot

        except Exception as e:
            self.logger.error(f"截取屏幕区域失败: {str(e)}")
            return None
