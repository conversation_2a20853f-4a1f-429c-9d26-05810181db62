# -*- coding: utf-8 -*-
"""
GUI OCR操作模块
包含所有OCR相关的操作和区域选择功能
"""

import threading
from tkinter import messagebox

from config import COMPASS_SOFTWARE, update_ocr_region_config, update_buy_signal_region_config, update_status_region_config, APP_CONFIG
from region_selector import RegionSelector
from compass_automator import CompassAutomator


class GUIOCROperationsMixin:
    """GUI OCR操作Mixin类"""
    
    def load_saved_region(self):
        """读取配置文件中保存的区域信息"""
        try:
            if COMPASS_SOFTWARE.get('ocr_region'):
                region = COMPASS_SOFTWARE['ocr_region']
                x, y, width, height = region['x'], region['y'], region['width'], region['height']
                
                # 检查区域配置是否有效（非默认值）
                if not (x == 100 and y == 200 and width == 300 and height == 100):
                    self.selected_region = (x, y, width, height)
                    self.logger.info(f"加载已保存的区域配置: ({x}, {y}) 尺寸: {width}×{height}")
                    return True
                    
        except Exception as e:
            self.logger.error(f"读取区域配置失败: {str(e)}")
            
        return False
    
    def load_saved_buy_signal_region(self):
        """读取配置文件中保存的买入信号区域信息"""
        try:
            if COMPASS_SOFTWARE.get('buy_signal_region'):
                region = COMPASS_SOFTWARE['buy_signal_region']
                x, y, width, height = region['x'], region['y'], region['width'], region['height']
                
                # 检查区域配置是否有效（非默认值）
                if not (x == 0 and y == 0 and width == 100 and height == 50):
                    self.selected_buy_signal_region = (x, y, width, height)
                    self.logger.info(f"加载已保存的买入信号区域配置: ({x}, {y}) 尺寸: {width}×{height}")
                    return True
                    
        except Exception as e:
            self.logger.error(f"读取买入信号区域配置失败: {str(e)}")
            
        return False
    
    def load_saved_status_region(self):
        """读取配置文件中保存的状态区域信息"""
        try:
            if COMPASS_SOFTWARE.get('status_region'):
                region = COMPASS_SOFTWARE['status_region']
                x, y, width, height = region['x'], region['y'], region['width'], region['height']
                
                # 检查区域配置是否有效（非默认值）
                if not (x == 100 and y == 100 and width == 100 and height == 30):
                    self.selected_status_region = (x, y, width, height)
                    self.logger.info(f"加载已保存的状态区域配置: ({x}, {y}) 尺寸: {width}×{height}")
                    return True
                    
        except Exception as e:
            self.logger.error(f"读取状态区域配置失败: {str(e)}")
            
        return False
    
    def init_ocr_early(self):
        """程序启动时提前初始化OCR模块"""
        try:
            # 使用OCR管理器初始化（自动从配置读取参数）
            if self.ocr_manager.initialize_ocr():
                status = self.ocr_manager.get_engine_status()
                available_engines = [name for name, available in status['available_engines'].items() if available]
                self.logger.info(f"OCR模块提前初始化成功，可用引擎: {', '.join(available_engines)}")
                return True
            else:
                self.logger.error("OCR模块初始化失败：无可用引擎")
                return False
                
        except Exception as e:
            self.logger.error(f"OCR模块提前初始化失败: {str(e)}")
            return False
    
    def init_compass_early(self):
        """程序启动时提前初始化指南针连接"""
        try:
            self.logger.info("开始提前初始化指南针连接...")

            # 创建CompassAutomator实例，传递OCR管理器和鼠标重置回调函数
            from compass_automator import CompassAutomator
            self.compass_automator = CompassAutomator(ocr_manager=self.ocr_manager, mouse_reset_callback=self.move_mouse_to_target_position)

            # 尝试连接到指南针软件（非阻塞方式）
            # 注意：这里不强制要求连接成功，因为用户可能还没有启动指南针软件
            # 只是提前创建实例，避免后续重复创建
            self.logger.info("指南针自动化器实例已创建（包含OCR管理器），连接将在开始分析时建立")
            return True

        except Exception as e:
            self.logger.error(f"指南针连接提前初始化失败: {str(e)}")
            self.compass_automator = None
            return False
    
    def update_ui_after_init(self):
        """程序初始化后更新界面状态"""
        # 更新OCR状态显示
        if self.ocr_manager.is_initialized():
            self._update_ocr_status()
        
        # 如果有已保存的区域，更新界面显示
        if self.selected_region:
            x, y, width, height = self.selected_region
            region_text = f"区域: ({x}, {y}) 尺寸: {width}×{height}"
            self.region_info_var.set(region_text)
            
            self.logger.info(f"界面已更新，显示保存的区域配置: {region_text}")
        else:
            self.region_info_var.set("未选择区域")
        
        # 如果有已保存的买入信号区域，更新界面显示
        if hasattr(self, 'selected_buy_signal_region') and self.selected_buy_signal_region:
            x, y, width, height = self.selected_buy_signal_region
            region_text = f"买入信号区域: ({x}, {y}) 尺寸: {width}×{height}"
            self.buy_signal_region_info_var.set(region_text)
            
            self.logger.info(f"界面已更新，显示保存的买入信号区域配置: {region_text}")
        else:
            self.buy_signal_region_info_var.set("未选择买入信号区域")
        
        # 如果有已保存的状态区域，更新界面显示
        if hasattr(self, 'selected_status_region') and self.selected_status_region:
            x, y, width, height = self.selected_status_region
            region_text = f"状态区域: ({x}, {y}) 尺寸: {width}×{height}"
            self.status_region_info_var.set(region_text)
            
            self.logger.info(f"界面已更新，显示保存的状态区域配置: {region_text}")
        else:
            self.status_region_info_var.set("未选择状态区域")
        
        # 如果有已保存的鼠标基准位置，更新界面显示
        if hasattr(self, 'mouse_target_position') and self.mouse_target_position:
            x, y = self.mouse_target_position
            position_text = f"基准位置: ({x}, {y})"
            if hasattr(self, 'mouse_position_var'):
                self.mouse_position_var.set(position_text)
            
            self.logger.info(f"界面已更新，显示保存的鼠标基准位置配置: {position_text}")
        else:
            if hasattr(self, 'mouse_position_var'):
                self.mouse_position_var.set("未设置基准位置")
        
        # 加载系统设置配置
        if hasattr(self, 'load_system_config'):
            self.load_system_config()
        
    def select_screen_region(self):
        """选择屏幕区域"""
        try:
            self.logger.info("开始选择屏幕区域...")
            
            # 创建区域选择器并设置回调
            selector = RegionSelector(callback=self.on_region_selected)
            
            # 开始选择
            region = selector.start_selection()
            
            if region:
                self.selected_region = region
                self.logger.info(f"区域选择完成: {region}")
            else:
                self.logger.info("用户取消了区域选择")
                
        except Exception as e:
            self.logger.error(f"区域选择失败: {str(e)}")
            messagebox.showerror("错误", f"区域选择失败: {str(e)}")
    
    def on_region_selected(self, x: int, y: int, width: int, height: int):
        """区域选择完成回调"""
        self.selected_region = (x, y, width, height)
        
        # 更新区域信息显示
        region_text = f"区域: ({x}, {y}) 尺寸: {width}×{height}"
        self.region_info_var.set(region_text)
        
        self.logger.info(f"选择区域: {region_text}")
        
        # 保存区域配置到配置文件
        try:
            if update_ocr_region_config(x, y, width, height):
                self.logger.info("区域配置已保存到配置文件")
            else:
                self.logger.warning("保存区域配置到配置文件失败")
        except Exception as e:
            self.logger.error(f"保存区域配置失败: {str(e)}")
        
        # 由于OCR已在程序启动时初始化，这里不再需要初始化
        # 只需要更新OCR状态显示
        if self.ocr_manager.is_initialized():
            self._update_ocr_status()
    
    def select_buy_signal_region(self):
        """选择买入信号区域"""
        try:
            self.logger.info("开始选择买入信号区域...")
            
            # 创建区域选择器并设置回调
            selector = RegionSelector(callback=self.on_buy_signal_region_selected)
            
            # 开始选择
            region = selector.start_selection()
            
            if region:
                self.selected_buy_signal_region = region
                self.logger.info(f"买入信号区域选择完成: {region}")
            else:
                self.logger.info("用户取消了买入信号区域选择")
                
        except Exception as e:
            self.logger.error(f"买入信号区域选择失败: {str(e)}")
            messagebox.showerror("错误", f"买入信号区域选择失败: {str(e)}")
    
    def on_buy_signal_region_selected(self, x: int, y: int, width: int, height: int):
        """买入信号区域选择完成回调"""
        self.selected_buy_signal_region = (x, y, width, height)
        
        # 更新买入信号区域信息显示
        region_text = f"买入信号区域: ({x}, {y}) 尺寸: {width}×{height}"
        self.buy_signal_region_info_var.set(region_text)
        
        self.logger.info(f"选择买入信号区域: {region_text}")
        
        # 保存买入信号区域配置到配置文件
        try:
            if update_buy_signal_region_config(x, y, width, height):
                self.logger.info("买入信号区域配置已保存到配置文件")
            else:
                self.logger.warning("保存买入信号区域配置到配置文件失败")
        except Exception as e:
            self.logger.error(f"保存买入信号区域配置失败: {str(e)}")
    
    def select_status_region(self):
        """选择状态区域"""
        try:
            self.logger.info("开始选择状态区域...")
            
            # 创建区域选择器并设置回调
            selector = RegionSelector(callback=self.on_status_region_selected)
            
            # 开始选择
            region = selector.start_selection()
            
            if region:
                self.selected_status_region = region
                self.logger.info(f"状态区域选择完成: {region}")
            else:
                self.logger.info("用户取消了状态区域选择")
                
        except Exception as e:
            self.logger.error(f"状态区域选择失败: {str(e)}")
            messagebox.showerror("错误", f"状态区域选择失败: {str(e)}")
    
    def on_status_region_selected(self, x: int, y: int, width: int, height: int):
        """状态区域选择完成回调"""
        self.selected_status_region = (x, y, width, height)
        
        # 更新状态区域信息显示
        region_text = f"状态区域: ({x}, {y}) 尺寸: {width}×{height}"
        self.status_region_info_var.set(region_text)
        
        self.logger.info(f"选择状态区域: {region_text}")
        
        # 保存状态区域配置到配置文件
        try:
            if update_status_region_config(x, y, width, height):
                self.logger.info("状态区域配置已保存到配置文件")
            else:
                self.logger.warning("保存状态区域配置到配置文件失败")
        except Exception as e:
            self.logger.error(f"保存状态区域配置失败: {str(e)}")
    
    def run_ocr_diagnostics(self):
        """运行OCR诊断"""
        try:
            self.logger.info("开始OCR诊断...")
            
            def diagnose_thread():
                try:
                    try:
                        from ocr_diagnostics import OCRDiagnostics
                    except ImportError:
                        error_msg = "OCR诊断模块已移至备份目录，诊断功能暂时不可用"
                        self.message_queue.put(("ocr_diagnosis_error", error_msg))
                        return
                    
                    # 运行诊断
                    diagnostics = OCRDiagnostics()
                    report = diagnostics.run_full_diagnostics()
                    
                    # 通过消息队列返回结果
                    self.message_queue.put(("ocr_diagnosis_result", report))
                    
                except Exception as e:
                    self.message_queue.put(("ocr_diagnosis_error", str(e)))
            
            threading.Thread(target=diagnose_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"OCR诊断失败: {str(e)}")
            messagebox.showerror("错误", f"OCR诊断失败: {str(e)}")
    
    def test_fund_ocr_recognition(self):
        """测试多空资金OCR识别"""
        if not self.selected_region:
            messagebox.showwarning("警告", "请先选择屏幕区域")
            return
        
        if not self.ocr_manager.is_initialized():
            messagebox.showerror("错误", "OCR引擎未初始化")
            return
        
        try:
            self.logger.info("开始测试多空资金OCR识别...")
            
            # 检查OCR引擎状态
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            
            if not available_engines:
                messagebox.showerror("错误", "没有可用的OCR引擎，请运行OCR诊断检查问题")
                return
            
            # 更新状态显示
            self.ocr_status_label.config(text="OCR状态: 测试多空资金识别中...", foreground="blue")
            
            # 在新线程中执行OCR测试以避免阻塞GUI
            def test_fund_thread():
                try:
                    x, y, width, height = self.selected_region
                    
                    # 重置错误计数
                    self.ocr_manager.reset_error_count()
                    
                    # 获取OCR引擎并测试
                    ocr_engine = self.ocr_manager.get_optimized_ocr_engine()
                    if ocr_engine:
                        if hasattr(ocr_engine, 'test_fund_data_ocr_recognition_optimized'):
                            result = ocr_engine.test_fund_data_ocr_recognition_optimized(x, y, width, height)
                        else:
                            result = ocr_engine.test_fund_data_ocr_recognition(x, y, width, height)
                        # 通过消息队列返回结果
                        self.message_queue.put(("fund_ocr_test_result", result))
                    else:
                        self.message_queue.put(("fund_ocr_test_error", "无法获取OCR引擎"))
                    
                except Exception as e:
                    self.message_queue.put(("fund_ocr_test_error", str(e)))
                finally:
                    # 重新启用按钮
                    self.message_queue.put(("fund_ocr_test_complete", None))
            
            threading.Thread(target=test_fund_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"多空资金OCR测试失败: {str(e)}")
            messagebox.showerror("错误", f"多空资金OCR测试失败: {str(e)}")
            self._update_ocr_status()
    
    def test_ocr_recognition(self):
        """测试OCR识别"""
        if not self.selected_region:
            messagebox.showwarning("警告", "请先选择屏幕区域")
            return
        
        if not self.ocr_manager.is_initialized():
            messagebox.showerror("错误", "OCR引擎未初始化")
            return
        
        try:
            self.logger.info("开始测试OCR识别...")
            
            # 检查OCR引擎状态
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            
            if not available_engines:
                messagebox.showerror("错误", "没有可用的OCR引擎，请运行OCR诊断检查问题")
                return
            
            # 更新状态显示
            self.ocr_status_label.config(text="OCR状态: 测试中...", foreground="blue")
            
            # 在新线程中执行OCR测试以避免阻塞GUI
            def test_thread():
                try:
                    x, y, width, height = self.selected_region
                    
                    # 重置错误计数
                    self.ocr_manager.reset_error_count()
                    
                    # 获取OCR引擎并测试
                    fund_ocr = self.ocr_manager.get_ocr_engine()
                    if fund_ocr:
                        result = fund_ocr.test_raw_ocr_recognition(x, y, width, height)
                        # 通过消息队列返回结果
                        self.message_queue.put(("ocr_raw_test_result", result))
                    else:
                        self.message_queue.put(("ocr_test_error", "无法获取OCR引擎"))
                    
                except Exception as e:
                    self.message_queue.put(("ocr_test_error", str(e)))
                finally:
                    # 重新启用按钮
                    self.message_queue.put(("ocr_test_complete", None))
            
            threading.Thread(target=test_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"OCR测试失败: {str(e)}")
            messagebox.showerror("错误", f"OCR测试失败: {str(e)}")
            self._update_ocr_status()
    
    def _update_ocr_status(self):
        """更新OCR状态显示"""
        if self.ocr_manager.is_initialized():
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            error_count = status.get('error_count', 0)
            
            if available_engines:
                if error_count > 0:
                    status_text = f"OCR状态: 运行中 ({', '.join(available_engines)}) - 错误:{error_count}"
                    status_color = "orange"
                else:
                    status_text = f"OCR状态: 就绪 ({', '.join(available_engines)})"
                    status_color = "green"
            else:
                status_text = "OCR状态: 无可用引擎"
                status_color = "red"
                
            self.ocr_status_label.config(text=status_text, foreground=status_color)
        else:
            self.ocr_status_label.config(text="OCR状态: 未初始化", foreground="orange")
            
            
    def test_connection(self):
        """测试指南针连接"""
        def test_thread():
            try:
                self.message_queue.put(("status", "正在测试连接..."))
                self.message_queue.put(("progress", 50))
                
                automator = CompassAutomator(ocr_manager=self.ocr_manager)
                success = automator.start_compass_software()
                
                if success:
                    self.message_queue.put(("log", "指南针连接测试成功"))
                    self.message_queue.put(("status", "连接测试成功"))
                    automator.close_compass_software()
                else:
                    self.message_queue.put(("log", "指南针连接测试失败"))
                    self.message_queue.put(("status", "连接测试失败"))
                    
            except Exception as e:
                self.message_queue.put(("log", f"连接测试错误: {str(e)}"))
                self.message_queue.put(("status", "连接测试错误"))
            finally:
                self.message_queue.put(("progress", 0))
                
        threading.Thread(target=test_thread, daemon=True).start()
    
    def validate_fund_ocr_methods(self):
        """验证多空资金OCR识别方法"""
        if not self.selected_region:
            messagebox.showwarning("警告", "请先选择屏幕区域")
            return
        
        try:
            # 导入验证器
            from fund_ocr_validator import FundOCRValidator
            
            self.logger.info("开始多空资金OCR方法验证...")
            
            # 更新状态显示
            self.ocr_status_label.config(text="OCR状态: 正在验证多种识别方法...", foreground="blue")
            
            # 在新线程中执行验证以避免阻塞GUI
            def validate_thread():
                try:
                    x, y, width, height = self.selected_region
                    
                    # 创建验证器实例
                    validator = FundOCRValidator(debug_mode=True)
                    
                    # 执行验证
                    validation_result = validator.validate_fund_ocr(x, y, width, height)
                    
                    # 通过消息队列返回结果
                    self.message_queue.put(("fund_ocr_validation_result", validation_result))
                    
                except Exception as e:
                    self.message_queue.put(("fund_ocr_validation_error", str(e)))
                finally:
                    # 重新启用按钮
                    self.message_queue.put(("fund_ocr_validation_complete", None))
            
            threading.Thread(target=validate_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"多空资金OCR验证失败: {str(e)}")
            messagebox.showerror("错误", f"多空资金OCR验证失败: {str(e)}")
            self._update_ocr_status()